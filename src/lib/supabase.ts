import { createClient } from '@supabase/supabase-js';
import { env } from '$env/dynamic/public';

const supabaseUrl = env.PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = env.PUBLIC_SUPABASE_ANON_KEY || '';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Types for our database tables
export interface Place {
	id: number;
	name: string;
	description?: string;
	category: string;
	latitude: number;
	longitude: number;
	address?: string;
	website?: string;
	phone?: string;
	rating?: number;
	created_at: string;
	updated_at: string;
	user_id?: string;
}

export interface Category {
	id: number;
	name: string;
	icon: string;
	color: string;
	description?: string;
}

export interface Review {
	id: number;
	place_id: number;
	user_id: string;
	rating: number;
	comment?: string;
	created_at: string;
}

// Database functions
export async function getPlaces() {
	const { data, error } = await supabase
		.from('places')
		.select('*')
		.order('created_at', { ascending: false });

	if (error) {
		console.error('Error fetching places:', error);
		return [];
	}

	return data as Place[];
}

export async function getPlacesByCategory(category: string) {
	const { data, error } = await supabase
		.from('places')
		.select('*')
		.eq('category', category)
		.order('created_at', { ascending: false });

	if (error) {
		console.error('Error fetching places by category:', error);
		return [];
	}

	return data as Place[];
}

export async function createPlace(place: Omit<Place, 'id' | 'created_at' | 'updated_at'>) {
	console.log('🔄 Creating place with data:', place);

	const placeData = {
		name: place.name,
		description: place.description || null,
		category: place.category,
		latitude: place.latitude,
		longitude: place.longitude,
		address: place.address || null,
		website: place.website || null,
		phone: place.phone || null,
		rating: place.rating || 0
	};

	console.log('📤 Sending to Supabase:', placeData);

	const { data, error } = await supabase
		.from('places')
		.insert([placeData])
		.select()
		.single();

	if (error) {
		console.error('❌ Supabase error details:', {
			message: error.message,
			details: error.details,
			hint: error.hint,
			code: error.code
		});
		console.error('❌ Full error object:', error);
		throw new Error(`Chyba při ukládání: ${error.message}`);
	}

	console.log('✅ Place created successfully:', data);
	return data as Place;
}

export async function updatePlace(id: number, updates: Partial<Place>) {
	const { data, error } = await supabase
		.from('places')
		.update(updates)
		.eq('id', id)
		.select()
		.single();

	if (error) {
		console.error('Error updating place:', error);
		throw error;
	}

	return data as Place;
}

export async function deletePlace(id: number) {
	const { error } = await supabase
		.from('places')
		.delete()
		.eq('id', id);

	if (error) {
		console.error('Error deleting place:', error);
		throw error;
	}
}
