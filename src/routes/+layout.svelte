<script lang="ts">
	import '../app.css';

	let { children } = $props();
</script>

<div class="min-h-screen bg-gray-50">
	<!-- Header -->
	<header class="bg-white shadow-sm border-b">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="flex justify-between items-center h-16">
				<div class="flex items-center">
					<h1 class="text-xl font-bold text-gray-900">🗺️ Mapa Brna</h1>
				</div>
				<nav class="hidden md:flex space-x-8">
					<a href="/" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">Mapa</a>
					<a href="/places" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">M<PERSON><PERSON></a>
					<a href="/add" class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">P<PERSON>ida<PERSON> místo</a>
				</nav>
				<!-- Mobile menu button -->
				<div class="md:hidden">
					<button class="text-gray-700 hover:text-blue-600" aria-label="Otevřít menu">
						<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
						</svg>
					</button>
				</div>
			</div>
		</div>
	</header>

	<!-- Main content -->
	<main class="flex-1">
		{@render children()}
	</main>
</div>
