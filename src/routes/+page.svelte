<script lang="ts">
	import { onMount } from 'svelte';
	import mapboxgl from 'mapbox-gl';
	import { env } from '$env/dynamic/public';
	import { getPlaces, type Place } from '$lib/supabase';

	let mapContainer: HTMLDivElement;
	let map: mapboxgl.Map;
	let places: Place[] = [];
	let markers: mapboxgl.Marker[] = [];
	let filteredPlaces: Place[] = [];
	let selectedCategories: Set<string> = new Set();
	let searchTerm = '';

	onMount(() => {
		mapboxgl.accessToken = env.PUBLIC_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw';

		map = new mapboxgl.Map({
			container: mapContainer,
			style: 'mapbox://styles/mapbox/streets-v12',
			center: [16.6068, 49.1951], // Brno coordinates
			zoom: 12
		});

		// Add navigation controls
		map.addControl(new mapboxgl.NavigationControl());

		// Add geolocate control
		map.addControl(
			new mapboxgl.GeolocateControl({
				positionOptions: {
					enableHighAccuracy: true
				},
				trackUserLocation: true,
				showUserHeading: true
			})
		);

		// Load places when map is ready
		map.on('load', loadPlaces);

		return () => {
			map?.remove();
		};
	});

	const categoryIcons: Record<string, string> = {
		toilet: '🚻',
		wifi: '📶',
		running: '🏃',
		park: '🌳',
		shop: '🏪',
		cafe: '☕',
		restaurant: '🍺'
	};

	// Reactive filtering
	$: {
		filteredPlaces = places.filter(place => {
			// Search filter
			const matchesSearch = !searchTerm ||
				place.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				place.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
				place.address?.toLowerCase().includes(searchTerm.toLowerCase());

			// Category filter
			const matchesCategory = selectedCategories.size === 0 || selectedCategories.has(place.category);

			return matchesSearch && matchesCategory;
		});

		// Update markers when filters change
		if (map && places.length > 0) {
			addMarkersToMap();
		}
	}

	async function loadPlaces() {
		try {
			places = await getPlaces();
		} catch (error) {
			console.error('Error loading places:', error);
		}
	}

	function toggleCategory(category: string) {
		if (selectedCategories.has(category)) {
			selectedCategories.delete(category);
		} else {
			selectedCategories.add(category);
		}
		selectedCategories = selectedCategories; // Trigger reactivity
	}

	function addMarkersToMap() {
		// Clear existing markers
		markers.forEach(marker => marker.remove());
		markers = [];

		// Add new markers for filtered places
		filteredPlaces.forEach(place => {
			// Create popup content
			const popupContent = `
				<div class="p-2">
					<div class="flex items-center space-x-2 mb-2">
						<span class="text-lg">${categoryIcons[place.category] || '📍'}</span>
						<h3 class="font-semibold text-gray-900">${place.name}</h3>
					</div>
					${place.description ? `<p class="text-sm text-gray-600 mb-2">${place.description}</p>` : ''}
					${place.address ? `<p class="text-xs text-gray-500 mb-2">📍 ${place.address}</p>` : ''}
					<div class="flex items-center justify-between">
						<span class="text-xs text-yellow-600">⭐ ${place.rating || 0}</span>
						<a href="/places" class="text-xs text-blue-600 hover:text-blue-800">Detail →</a>
					</div>
				</div>
			`;

			// Create popup
			const popup = new mapboxgl.Popup({ offset: 25 })
				.setHTML(popupContent);

			// Create marker
			const marker = new mapboxgl.Marker({
				color: getCategoryColor(place.category)
			})
				.setLngLat([place.longitude, place.latitude])
				.setPopup(popup)
				.addTo(map);

			markers.push(marker);
		});
	}

	function getCategoryColor(category: string): string {
		const colors: Record<string, string> = {
			toilet: '#3B82F6',
			wifi: '#10B981',
			running: '#F59E0B',
			park: '#059669',
			shop: '#8B5CF6',
			cafe: '#EF4444',
			restaurant: '#F97316'
		};
		return colors[category] || '#6B7280';
	}
</script>

<svelte:head>
	<title>Mapa Brna - Užitečná místa</title>
</svelte:head>

<div class="h-screen relative">
	<!-- Map container -->
	<div bind:this={mapContainer} class="w-full h-full"></div>

	<!-- Floating search and filters -->
	<div class="absolute top-4 left-4 right-4 z-10">
		<div class="bg-white rounded-lg shadow-lg p-4 max-w-md">
			<div class="flex items-center space-x-2 mb-3">
				<input
					type="text"
					placeholder="Hledat místa..."
					bind:value={searchTerm}
					class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
				/>
				<div class="text-sm text-gray-500 px-2">
					{filteredPlaces.length} z {places.length}
				</div>
			</div>

			<!-- Category filters -->
			<div class="flex flex-wrap gap-2">
				<button
					on:click={() => toggleCategory('toilet')}
					class="px-3 py-1 rounded-full text-sm transition-colors {selectedCategories.has('toilet') ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}"
				>
					🚻 Toalety
				</button>
				<button
					on:click={() => toggleCategory('wifi')}
					class="px-3 py-1 rounded-full text-sm transition-colors {selectedCategories.has('wifi') ? 'bg-green-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}"
				>
					📶 WiFi
				</button>
				<button
					on:click={() => toggleCategory('running')}
					class="px-3 py-1 rounded-full text-sm transition-colors {selectedCategories.has('running') ? 'bg-yellow-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}"
				>
					🏃 Běhání
				</button>
				<button
					on:click={() => toggleCategory('park')}
					class="px-3 py-1 rounded-full text-sm transition-colors {selectedCategories.has('park') ? 'bg-green-700 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}"
				>
					🌳 Parky
				</button>
				<button
					on:click={() => toggleCategory('shop')}
					class="px-3 py-1 rounded-full text-sm transition-colors {selectedCategories.has('shop') ? 'bg-purple-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}"
				>
					🏪 Obchody
				</button>
				<button
					on:click={() => toggleCategory('cafe')}
					class="px-3 py-1 rounded-full text-sm transition-colors {selectedCategories.has('cafe') ? 'bg-red-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}"
				>
					☕ Kavárny
				</button>
			</div>
		</div>
	</div>

	<!-- Add place button -->
	<div class="absolute bottom-6 right-6 z-10">
		<a
			href="/add"
			class="bg-blue-600 text-white p-4 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
			aria-label="Přidat nové místo"
		>
			<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
			</svg>
		</a>
	</div>
</div>
