<script lang="ts">
	import { onMount } from 'svelte';
	import mapboxgl from 'mapbox-gl';
	import { env } from '$env/dynamic/public';
	import { getPlaces, type Place } from '$lib/supabase';
	import MapStyleSwitcher from '$lib/components/MapStyleSwitcher.svelte';

	let mapContainer: HTMLDivElement;
	let map: mapboxgl.Map;
	let places = $state<Place[]>([]);
	let markers: mapboxgl.Marker[] = [];
	let filteredPlaces = $state<Place[]>([]);
	let selectedCategories = $state(new Set<string>());
	let searchTerm = $state('');
	let currentMapStyle = $state('auto');
	let searchPanelExpanded = $state(false);
	let stylePanelExpanded = $state(false);

	onMount(() => {
		mapboxgl.accessToken = env.PUBLIC_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw';

		// Get initial style based on time
		const initialStyle = getTimeBasedStyle();

		map = new mapboxgl.Map({
			container: mapContainer,
			style: initialStyle,
			center: [16.6068, 49.1951], // Brno coordinates
			zoom: 12,
			pitch: 45, // Úhel náklonu pro 3D efekt
			bearing: 0, // Rotace mapy
			antialias: true // Vyhlazování pro lepší kvalitu
		});

		// Add navigation controls
		map.addControl(new mapboxgl.NavigationControl());

		// Add geolocate control
		map.addControl(
			new mapboxgl.GeolocateControl({
				positionOptions: {
					enableHighAccuracy: true
				},
				trackUserLocation: true,
				showUserHeading: true
			})
		);

		// Load places when map is ready
		map.on('load', () => {
			// Add 3D buildings layer
			map.addLayer({
				'id': '3d-buildings',
				'source': 'composite',
				'source-layer': 'building',
				'filter': ['==', 'extrude', 'true'],
				'type': 'fill-extrusion',
				'minzoom': 15,
				'paint': {
					'fill-extrusion-color': '#aaa',
					'fill-extrusion-height': [
						'interpolate',
						['linear'],
						['zoom'],
						15,
						0,
						15.05,
						['get', 'height']
					],
					'fill-extrusion-base': [
						'interpolate',
						['linear'],
						['zoom'],
						15,
						0,
						15.05,
						['get', 'min_height']
					],
					'fill-extrusion-opacity': 0.6
				}
			});

			loadPlaces();
		});

		// Auto-update style every hour if in auto mode
		const styleUpdateInterval = setInterval(() => {
			if (currentMapStyle === 'auto') {
				const newStyle = getTimeBasedStyle();
				if (map.getStyle().name !== newStyle) {
					map.setStyle(newStyle);
					map.once('styledata', () => {
						// Re-add 3D buildings and markers after style change
						if (!map.getLayer('3d-buildings')) {
							map.addLayer({
								'id': '3d-buildings',
								'source': 'composite',
								'source-layer': 'building',
								'filter': ['==', 'extrude', 'true'],
								'type': 'fill-extrusion',
								'minzoom': 15,
								'paint': {
									'fill-extrusion-color': '#aaa',
									'fill-extrusion-height': [
										'interpolate',
										['linear'],
										['zoom'],
										15,
										0,
										15.05,
										['get', 'height']
									],
									'fill-extrusion-base': [
										'interpolate',
										['linear'],
										['zoom'],
										15,
										0,
										15.05,
										['get', 'min_height']
									],
									'fill-extrusion-opacity': 0.6
								}
							});
						}
						addMarkersToMap();
					});
				}
			}
		}, 60000); // Check every minute

		return () => {
			clearInterval(styleUpdateInterval);
			map?.remove();
		};
	});

	const categoryIcons: Record<string, string> = {
		toilet: '🚻',
		wifi: '📶',
		running: '🏃',
		park: '🌳',
		shop: '🏪',
		cafe: '☕',
		restaurant: '🍺'
	};

	// Reactive filtering
	$effect(() => {
		filteredPlaces = places.filter(place => {
			// Search filter
			const matchesSearch = !searchTerm ||
				place.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
				place.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
				place.address?.toLowerCase().includes(searchTerm.toLowerCase());

			// Category filter
			const matchesCategory = selectedCategories.size === 0 || selectedCategories.has(place.category);

			return matchesSearch && matchesCategory;
		});

		// Update markers when filters change
		if (map && places.length > 0) {
			addMarkersToMap();
		}
	});

	async function loadPlaces() {
		try {
			places = await getPlaces();
		} catch (error) {
			console.error('Error loading places:', error);
		}
	}

	function toggleCategory(category: string) {
		if (selectedCategories.has(category)) {
			selectedCategories.delete(category);
		} else {
			selectedCategories.add(category);
		}
		selectedCategories = selectedCategories; // Trigger reactivity
	}

	function addMarkersToMap() {
		// Clear existing markers
		markers.forEach(marker => marker.remove());
		markers = [];

		// Add new markers for filtered places
		filteredPlaces.forEach(place => {
			// Create popup content
			const popupContent = `
				<div class="p-2">
					<div class="flex items-center space-x-2 mb-2">
						<span class="text-lg">${categoryIcons[place.category] || '📍'}</span>
						<h3 class="font-semibold text-gray-900">${place.name}</h3>
					</div>
					${place.description ? `<p class="text-sm text-gray-600 mb-2">${place.description}</p>` : ''}
					${place.address ? `<p class="text-xs text-gray-500 mb-2">📍 ${place.address}</p>` : ''}
					<div class="flex items-center justify-between">
						<span class="text-xs text-yellow-600">⭐ ${place.rating || 0}</span>
						<a href="/places" class="text-xs text-blue-600 hover:text-blue-800">Detail →</a>
					</div>
				</div>
			`;

			// Create popup
			const popup = new mapboxgl.Popup({ offset: 25 })
				.setHTML(popupContent);

			// Create marker
			const marker = new mapboxgl.Marker({
				color: getCategoryColor(place.category)
			})
				.setLngLat([place.longitude, place.latitude])
				.setPopup(popup)
				.addTo(map);

			markers.push(marker);
		});
	}

	function getCategoryColor(category: string): string {
		const colors: Record<string, string> = {
			toilet: '#3B82F6',
			wifi: '#10B981',
			running: '#F59E0B',
			park: '#059669',
			shop: '#8B5CF6',
			cafe: '#EF4444',
			restaurant: '#F97316'
		};
		return colors[category] || '#6B7280';
	}

	// Get time-based map style
	function getTimeBasedStyle(): string {
		const hour = new Date().getHours();

		if (hour >= 6 && hour < 10) {
			return 'mapbox://styles/mapbox/light-v11'; // Dawn
		} else if (hour >= 10 && hour < 18) {
			return 'mapbox://styles/mapbox/streets-v12'; // Day
		} else if (hour >= 18 && hour < 21) {
			return 'mapbox://styles/mapbox/satellite-streets-v12'; // Dusk
		} else {
			return 'mapbox://styles/mapbox/dark-v11'; // Night
		}
	}



	function handleStyleChange(event: CustomEvent<{ style: string }>) {
		const newStyle = event.detail.style;

		if (newStyle === 'auto') {
			currentMapStyle = 'auto';
			const autoStyle = getTimeBasedStyle();
			map.setStyle(autoStyle);
		} else {
			currentMapStyle = newStyle;
			map.setStyle(newStyle);
		}

		// Re-add 3D buildings after style change
		map.once('styledata', () => {
			if (map.getLayer('3d-buildings')) {
				map.removeLayer('3d-buildings');
			}

			map.addLayer({
				'id': '3d-buildings',
				'source': 'composite',
				'source-layer': 'building',
				'filter': ['==', 'extrude', 'true'],
				'type': 'fill-extrusion',
				'minzoom': 15,
				'paint': {
					'fill-extrusion-color': '#aaa',
					'fill-extrusion-height': [
						'interpolate',
						['linear'],
						['zoom'],
						15,
						0,
						15.05,
						['get', 'height']
					],
					'fill-extrusion-base': [
						'interpolate',
						['linear'],
						['zoom'],
						15,
						0,
						15.05,
						['get', 'min_height']
					],
					'fill-extrusion-opacity': 0.6
				}
			});

			// Re-add markers
			addMarkersToMap();
		});
	}
</script>

<svelte:head>
	<title>Mapa Brna - Užitečná místa</title>
</svelte:head>

<div class="h-screen relative overflow-hidden">
	<!-- Map container -->
	<div bind:this={mapContainer} class="w-full h-full"></div>

	<!-- Floating controls -->
	<div class="absolute top-6 left-6 right-6 z-10">
		<div class="flex flex-col lg:flex-row gap-4 max-w-4xl">
			<!-- Search panel -->
			<div class="relative">
				<!-- Collapsed search button -->
				{#if !searchPanelExpanded}
					<button
						onclick={() => searchPanelExpanded = true}
						class="w-14 h-14 bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/20 flex items-center justify-center hover:scale-110 transition-all duration-300 group"
						aria-label="Otevřít vyhledávání"
					>
						<svg class="w-6 h-6 text-gray-700 group-hover:text-blue-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
						</svg>
					</button>
				{/if}

				<!-- Expanded search panel -->
				{#if searchPanelExpanded}
					<div class="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/20 p-6 w-80 animate-in slide-in-from-left-5 duration-300">
						<!-- Search header -->
						<div class="flex items-center justify-between mb-4">
							<div class="flex items-center space-x-3">
								<div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
									<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
									</svg>
								</div>
								<div>
									<h3 class="font-semibold text-gray-900">Vyhledat místa</h3>
									<p class="text-xs text-gray-500">{filteredPlaces.length} z {places.length} míst</p>
								</div>
							</div>
							<!-- Close button -->
							<button
								onclick={() => searchPanelExpanded = false}
								class="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100 transition-colors group"
								aria-label="Zavřít vyhledávání"
							>
								<svg class="w-4 h-4 text-gray-400 group-hover:text-gray-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
								</svg>
							</button>
						</div>

			<!-- Search input -->
			<div class="relative mb-4">
				<input
					type="text"
					placeholder="Hledat podle názvu, popisu nebo adresy..."
					bind:value={searchTerm}
					class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-sm"
				/>
				{#if searchTerm}
					<button
						onclick={() => searchTerm = ''}
						class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
						aria-label="Vymazat vyhledávání"
					>
						<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
						</svg>
					</button>
				{/if}
			</div>

			<!-- Category filters -->
			<div class="space-y-3">
				<h4 class="text-sm font-medium text-gray-700">Kategorie</h4>
				<div class="flex flex-wrap gap-2">
					<button
						onclick={() => toggleCategory('toilet')}
						class="px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200 {selectedCategories.has('toilet') ? 'bg-blue-600 text-white shadow-lg scale-105' : 'bg-gray-100 text-gray-700 hover:bg-blue-50 hover:text-blue-600'}"
					>
						🚻 Toalety
					</button>
					<button
						onclick={() => toggleCategory('wifi')}
						class="px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200 {selectedCategories.has('wifi') ? 'bg-green-600 text-white shadow-lg scale-105' : 'bg-gray-100 text-gray-700 hover:bg-green-50 hover:text-green-600'}"
					>
						📶 WiFi
					</button>
					<button
						onclick={() => toggleCategory('running')}
						class="px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200 {selectedCategories.has('running') ? 'bg-yellow-600 text-white shadow-lg scale-105' : 'bg-gray-100 text-gray-700 hover:bg-yellow-50 hover:text-yellow-600'}"
					>
						🏃 Běhání
					</button>
					<button
						onclick={() => toggleCategory('park')}
						class="px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200 {selectedCategories.has('park') ? 'bg-green-700 text-white shadow-lg scale-105' : 'bg-gray-100 text-gray-700 hover:bg-green-50 hover:text-green-600'}"
					>
						🌳 Parky
					</button>
					<button
						onclick={() => toggleCategory('shop')}
						class="px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200 {selectedCategories.has('shop') ? 'bg-purple-600 text-white shadow-lg scale-105' : 'bg-gray-100 text-gray-700 hover:bg-purple-50 hover:text-purple-600'}"
					>
						🏪 Obchody
					</button>
					<button
						onclick={() => toggleCategory('cafe')}
						class="px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200 {selectedCategories.has('cafe') ? 'bg-red-600 text-white shadow-lg scale-105' : 'bg-gray-100 text-gray-700 hover:bg-red-50 hover:text-red-600'}"
					>
						☕ Kavárny
					</button>
				</div>

							<!-- Clear filters button -->
							{#if selectedCategories.size > 0 || searchTerm}
								<button
									onclick={() => {
										selectedCategories.clear();
										selectedCategories = selectedCategories;
										searchTerm = '';
									}}
									class="w-full mt-3 px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors"
								>
									🗑️ Vymazat filtry
								</button>
							{/if}
						</div>
					</div>
				{/if}
			</div>

			<!-- Style switcher panel -->
			<div class="relative">
				<!-- Collapsed style button -->
				{#if !stylePanelExpanded}
					<button
						onclick={() => stylePanelExpanded = true}
						class="w-14 h-14 bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/20 flex items-center justify-center hover:scale-110 transition-all duration-300 group"
						aria-label="Otevřít styly mapy"
					>
						<svg class="w-6 h-6 text-gray-700 group-hover:text-purple-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
						</svg>
					</button>
				{/if}

				<!-- Expanded style panel -->
				{#if stylePanelExpanded}
					<div class="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/20 p-6 w-80 animate-in slide-in-from-right-5 duration-300">
						<!-- Style header -->
						<div class="flex items-center justify-between mb-4">
							<div class="flex items-center space-x-3">
								<div class="w-8 h-8 bg-gradient-to-br from-orange-500 to-pink-600 rounded-lg flex items-center justify-center">
									<span class="text-white text-xs">🎨</span>
								</div>
								<div>
									<h3 class="font-semibold text-gray-900">Styl mapy</h3>
									<p class="text-xs text-gray-500">Vzhled podle času</p>
								</div>
							</div>
							<!-- Close button -->
							<button
								onclick={() => stylePanelExpanded = false}
								class="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100 transition-colors group"
								aria-label="Zavřít styly"
							>
								<svg class="w-4 h-4 text-gray-400 group-hover:text-gray-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
								</svg>
							</button>
						</div>

						<MapStyleSwitcher
							bind:currentStyle={currentMapStyle}
							on:styleChange={handleStyleChange}
						/>
					</div>
				{/if}
			</div>
	</div>
</div>

	<!-- Add place button - Fixed position -->
	<div class="fixed bottom-6 right-6 z-50">
		<a
			href="/add"
			class="group bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-2xl shadow-2xl hover:shadow-3xl hover:scale-110 transition-all duration-300 flex items-center space-x-2"
			aria-label="Přidat nové místo"
		>
			<svg class="w-6 h-6 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
			</svg>
			<span class="hidden sm:block font-medium">Přidat místo</span>
		</a>
	</div>
</div>
