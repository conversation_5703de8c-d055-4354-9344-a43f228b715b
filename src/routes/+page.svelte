<script lang="ts">
	import { onMount } from 'svelte';
	import mapboxgl from 'mapbox-gl';
	import { env } from '$env/dynamic/public';

	let mapContainer: HTMLDivElement;
	let map: mapboxgl.Map;

	onMount(() => {
		mapboxgl.accessToken = env.PUBLIC_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw';

		map = new mapboxgl.Map({
			container: mapContainer,
			style: 'mapbox://styles/mapbox/streets-v12',
			center: [16.6068, 49.1951], // Brno coordinates
			zoom: 12
		});

		// Add navigation controls
		map.addControl(new mapboxgl.NavigationControl());

		// Add geolocate control
		map.addControl(
			new mapboxgl.GeolocateControl({
				positionOptions: {
					enableHighAccuracy: true
				},
				trackUserLocation: true,
				showUserHeading: true
			})
		);

		return () => {
			map?.remove();
		};
	});
</script>

<svelte:head>
	<title>Mapa Brna - Užitečná místa</title>
</svelte:head>

<div class="h-screen relative">
	<!-- Map container -->
	<div bind:this={mapContainer} class="w-full h-full"></div>

	<!-- Floating search and filters -->
	<div class="absolute top-4 left-4 right-4 z-10">
		<div class="bg-white rounded-lg shadow-lg p-4 max-w-md">
			<div class="flex items-center space-x-2 mb-3">
				<input
					type="text"
					placeholder="Hledat místa..."
					class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
				/>
				<button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
					🔍
				</button>
			</div>

			<!-- Category filters -->
			<div class="flex flex-wrap gap-2">
				<button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200">
					🚻 Toalety
				</button>
				<button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200">
					📶 WiFi
				</button>
				<button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200">
					🏃 Běhání
				</button>
				<button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200">
					🌳 Parky
				</button>
				<button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200">
					🏪 Obchody
				</button>
			</div>
		</div>
	</div>

	<!-- Add place button -->
	<div class="absolute bottom-6 right-6 z-10">
		<a
			href="/add"
			class="bg-blue-600 text-white p-4 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
			aria-label="Přidat nové místo"
		>
			<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
			</svg>
		</a>
	</div>
</div>
