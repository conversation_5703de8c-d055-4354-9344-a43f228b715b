<script lang="ts">
	import { onMount } from 'svelte';
	import mapboxgl from 'mapbox-gl';
	import { env } from '$env/dynamic/public';

	let mapContainer: HTMLDivElement;
	let map: mapboxgl.Map;
	let marker: mapboxgl.Marker;
	
	// Form data
	let formData = {
		name: '',
		category: '',
		description: '',
		address: '',
		website: '',
		phone: '',
		latitude: 49.1951,
		longitude: 16.6068
	};

	const categories = [
		{ value: 'toilet', label: '🚻 Veřejné toalety', icon: '🚻' },
		{ value: 'wifi', label: '📶 WiFi zdarma', icon: '📶' },
		{ value: 'running', label: '🏃 Místa na běhání', icon: '🏃' },
		{ value: 'park', label: '🌳 Parky', icon: '🌳' },
		{ value: 'shop', label: '🏪 Obchody/Večerky', icon: '🏪' },
		{ value: 'cafe', label: '☕ Kavárny', icon: '☕' },
		{ value: 'restaurant', label: '🍺 Restaurace/Bary', icon: '🍺' }
	];

	onMount(() => {
		mapboxgl.accessToken = env.PUBLIC_MAPBOX_ACCESS_TOKEN || 'pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw';
		
		map = new mapboxgl.Map({
			container: mapContainer,
			style: 'mapbox://styles/mapbox/streets-v12',
			center: [formData.longitude, formData.latitude],
			zoom: 14
		});

		// Add navigation controls
		map.addControl(new mapboxgl.NavigationControl());

		// Create draggable marker
		marker = new mapboxgl.Marker({ draggable: true })
			.setLngLat([formData.longitude, formData.latitude])
			.addTo(map);

		// Update coordinates when marker is dragged
		marker.on('dragend', () => {
			const lngLat = marker.getLngLat();
			formData.latitude = lngLat.lat;
			formData.longitude = lngLat.lng;
		});

		// Add click handler to move marker
		map.on('click', (e) => {
			formData.latitude = e.lngLat.lat;
			formData.longitude = e.lngLat.lng;
			marker.setLngLat([formData.longitude, formData.latitude]);
		});

		return () => {
			map?.remove();
		};
	});

	function handleSubmit() {
		// TODO: Odeslat data do Supabase
		console.log('Submitting place:', formData);
		alert('Místo bude přidáno po implementaci Supabase!');
	}
</script>

<svelte:head>
	<title>Přidat místo - Mapa Brna</title>
</svelte:head>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
	<div class="mb-8">
		<h1 class="text-3xl font-bold text-gray-900 mb-2">Přidat nové místo</h1>
		<p class="text-gray-600">Pomozte rozšířit mapu užitečných míst v Brně</p>
	</div>

	<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
		<!-- Form -->
		<div class="bg-white rounded-lg shadow-md p-6">
			<form on:submit|preventDefault={handleSubmit} class="space-y-6">
				<!-- Name -->
				<div>
					<label for="name" class="block text-sm font-medium text-gray-700 mb-2">
						Název místa *
					</label>
					<input
						id="name"
						type="text"
						bind:value={formData.name}
						required
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
						placeholder="např. Veřejné WC - Náměstí Svobody"
					/>
				</div>

				<!-- Category -->
				<div>
					<label for="category" class="block text-sm font-medium text-gray-700 mb-2">
						Kategorie *
					</label>
					<select
						id="category"
						bind:value={formData.category}
						required
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					>
						<option value="">Vyberte kategorii</option>
						{#each categories as category}
							<option value={category.value}>{category.label}</option>
						{/each}
					</select>
				</div>

				<!-- Description -->
				<div>
					<label for="description" class="block text-sm font-medium text-gray-700 mb-2">
						Popis
					</label>
					<textarea
						id="description"
						bind:value={formData.description}
						rows="3"
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
						placeholder="Krátký popis místa..."
					></textarea>
				</div>

				<!-- Address -->
				<div>
					<label for="address" class="block text-sm font-medium text-gray-700 mb-2">
						Adresa
					</label>
					<input
						id="address"
						type="text"
						bind:value={formData.address}
						class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
						placeholder="např. Náměstí Svobody 1, Brno"
					/>
				</div>

				<!-- Contact info -->
				<div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
					<div>
						<label for="website" class="block text-sm font-medium text-gray-700 mb-2">
							Webové stránky
						</label>
						<input
							id="website"
							type="url"
							bind:value={formData.website}
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder="https://..."
						/>
					</div>
					<div>
						<label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
							Telefon
						</label>
						<input
							id="phone"
							type="tel"
							bind:value={formData.phone}
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							placeholder="+420 123 456 789"
						/>
					</div>
				</div>

				<!-- Coordinates -->
				<div class="grid grid-cols-2 gap-4">
					<div>
						<label for="latitude" class="block text-sm font-medium text-gray-700 mb-2">
							Zeměpisná šířka
						</label>
						<input
							id="latitude"
							type="number"
							step="any"
							bind:value={formData.latitude}
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
						/>
					</div>
					<div>
						<label for="longitude" class="block text-sm font-medium text-gray-700 mb-2">
							Zeměpisná délka
						</label>
						<input
							id="longitude"
							type="number"
							step="any"
							bind:value={formData.longitude}
							class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
						/>
					</div>
				</div>

				<!-- Submit button -->
				<div class="flex justify-end space-x-4">
					<a
						href="/"
						class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
					>
						Zrušit
					</a>
					<button
						type="submit"
						class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
					>
						Přidat místo
					</button>
				</div>
			</form>
		</div>

		<!-- Map -->
		<div class="bg-white rounded-lg shadow-md overflow-hidden">
			<div class="p-4 border-b">
				<h3 class="text-lg font-medium text-gray-900">Umístění na mapě</h3>
				<p class="text-sm text-gray-600">Klikněte na mapu nebo přetáhněte značku pro nastavení polohy</p>
			</div>
			<div bind:this={mapContainer} class="h-96"></div>
		</div>
	</div>
</div>
