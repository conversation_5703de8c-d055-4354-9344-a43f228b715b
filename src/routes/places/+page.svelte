<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { getPlaces, getPlacesByCategory, type Place } from '$lib/supabase';

	let places: Place[] = [];
	let loading = true;
	let error = '';
	let searchTerm = '';
	let selectedCategory = '';
	let showSuccessMessage = false;

	// Load places on component mount
	onMount(async () => {
		// Check for success parameter in URL
		if (browser) {
			const urlParams = new URLSearchParams(window.location.search);
			if (urlParams.get('success') === 'added') {
				showSuccessMessage = true;
				// Remove the parameter from URL
				window.history.replaceState({}, '', window.location.pathname);
				// Hide message after 5 seconds
				setTimeout(() => {
					showSuccessMessage = false;
				}, 5000);
			}
		}

		await loadPlaces();
	});

	async function loadPlaces() {
		try {
			loading = true;
			error = '';

			if (selectedCategory) {
				places = await getPlacesByCategory(selectedCategory);
			} else {
				places = await getPlaces();
			}
		} catch (err) {
			console.error('Error loading places:', err);
			error = 'Chyba při načítání míst. Zkuste to znovu.';
		} finally {
			loading = false;
		}
	}

	// Filter places based on search term
	$: filteredPlaces = places.filter(place =>
		place.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
		place.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
		place.address?.toLowerCase().includes(searchTerm.toLowerCase())
	);

	// Handle category filter change
	async function handleCategoryChange() {
		await loadPlaces();
	}

	const categoryIcons: Record<string, string> = {
		toilet: '🚻',
		wifi: '📶',
		running: '🏃',
		park: '🌳',
		shop: '🏪',
		cafe: '☕'
	};

	const categoryNames: Record<string, string> = {
		toilet: 'Toalety',
		wifi: 'WiFi',
		running: 'Běhání',
		park: 'Parky',
		shop: 'Obchody',
		cafe: 'Kavárny'
	};
</script>

<svelte:head>
	<title>Místa - Mapa Brna</title>
</svelte:head>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
	<!-- Success message -->
	{#if showSuccessMessage}
		<div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
			<div class="flex">
				<div class="text-green-400">✅</div>
				<div class="ml-3">
					<h3 class="text-sm font-medium text-green-800">Místo bylo úspěšně přidáno!</h3>
					<p class="mt-1 text-sm text-green-700">Děkujeme za přispění do mapy Brna.</p>
				</div>
				<button
					on:click={() => showSuccessMessage = false}
					class="ml-auto text-green-400 hover:text-green-600"
				>
					✕
				</button>
			</div>
		</div>
	{/if}

	<div class="mb-8">
		<h1 class="text-3xl font-bold text-gray-900 mb-4">Všechna místa</h1>

		<!-- Search and filters -->
		<div class="flex flex-col sm:flex-row gap-4 mb-6">
			<div class="flex-1">
				<input
					type="text"
					placeholder="Hledat místa..."
					bind:value={searchTerm}
					class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
				/>
			</div>
			<select
				bind:value={selectedCategory}
				on:change={handleCategoryChange}
				class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
			>
				<option value="">Všechny kategorie</option>
				<option value="toilet">🚻 Toalety</option>
				<option value="wifi">📶 WiFi</option>
				<option value="running">🏃 Běhání</option>
				<option value="park">🌳 Parky</option>
				<option value="shop">🏪 Obchody</option>
				<option value="cafe">☕ Kavárny</option>
				<option value="restaurant">🍺 Restaurace</option>
			</select>
		</div>
	</div>

	<!-- Loading state -->
	{#if loading}
		<div class="text-center py-12">
			<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
			<p class="text-gray-600">Načítám místa...</p>
		</div>
	{:else if error}
		<!-- Error state -->
		<div class="text-center py-12">
			<div class="text-6xl mb-4">❌</div>
			<h3 class="text-lg font-medium text-gray-900 mb-2">Chyba při načítání</h3>
			<p class="text-gray-500 mb-6">{error}</p>
			<button
				on:click={loadPlaces}
				class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
			>
				Zkusit znovu
			</button>
		</div>
	{:else if filteredPlaces.length === 0}
		<!-- Empty state -->
		<div class="text-center py-12">
			<div class="text-6xl mb-4">🗺️</div>
			<h3 class="text-lg font-medium text-gray-900 mb-2">Žádná místa nenalezena</h3>
			<p class="text-gray-500 mb-6">
				{places.length === 0
					? 'Zatím nebyla přidána žádná místa. Buďte první!'
					: 'Zkuste změnit vyhledávací kritéria.'}
			</p>
			<a
				href="/add"
				class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
			>
				{places.length === 0 ? 'Přidat první místo' : 'Přidat nové místo'}
			</a>
		</div>
	{:else}
		<!-- Places grid -->
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
			{#each filteredPlaces as place}
				<div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6">
					<div class="flex items-start justify-between mb-3">
						<div class="flex items-center space-x-2">
							<span class="text-2xl">{categoryIcons[place.category]}</span>
							<span class="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
								{categoryNames[place.category]}
							</span>
						</div>
						<div class="flex items-center space-x-1">
							<span class="text-yellow-400">⭐</span>
							<span class="text-sm text-gray-600">{place.rating || 0}</span>
						</div>
					</div>

					<h3 class="text-lg font-semibold text-gray-900 mb-2">{place.name}</h3>
					<p class="text-gray-600 text-sm mb-3">{place.description || 'Bez popisu'}</p>
					<p class="text-gray-500 text-sm mb-4">📍 {place.address || 'Adresa neuvedena'}</p>

					<div class="flex items-center justify-between">
						<span class="text-sm text-gray-600">
							📅 {new Date(place.created_at).toLocaleDateString('cs-CZ')}
						</span>
						<button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
							Zobrazit detail →
						</button>
					</div>
				</div>
			{/each}
		</div>
	{/if}
</div>
