<script lang="ts">
	// TODO: Nač<PERSON>t místa ze Supabase
	const mockPlaces = [
		{
			id: 1,
			name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> WC - Náměstí Svobody',
			category: 'toilet',
			description: '<PERSON><PERSON><PERSON> veřejné toalety v centru města',
			address: 'Náměst<PERSON>, Brno',
			rating: 4.2,
			isOpen: true
		},
		{
			id: 2,
			name: '<PERSON>i<PERSON><PERSON> zdarma - Kavárna Slavia',
			category: 'wifi',
			description: 'Rychlé WiFi připojení zdarma pro zákazníky',
			address: 'Solniční 15, Brno',
			rating: 4.8,
			isOpen: true
		},
		{
			id: 3,
			name: '<PERSON><PERSON><PERSON><PERSON><PERSON> trasa - Lužánky',
			category: 'running',
			description: '<PERSON>rás<PERSON> běžecká trasa parkem s měkkým povrchem',
			address: 'Park Lužánky, Brno',
			rating: 4.5,
			isOpen: true
		}
	];

	const categoryIcons: Record<string, string> = {
		toilet: '🚻',
		wifi: '📶',
		running: '🏃',
		park: '🌳',
		shop: '🏪',
		cafe: '☕'
	};

	const categoryNames: Record<string, string> = {
		toilet: 'Toalety',
		wifi: 'WiFi',
		running: 'Běhání',
		park: 'Parky',
		shop: 'Obchody',
		cafe: 'Kavárny'
	};
</script>

<svelte:head>
	<title>Místa - Mapa Brna</title>
</svelte:head>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
	<div class="mb-8">
		<h1 class="text-3xl font-bold text-gray-900 mb-4">Všechna místa</h1>
		
		<!-- Search and filters -->
		<div class="flex flex-col sm:flex-row gap-4 mb-6">
			<div class="flex-1">
				<input 
					type="text" 
					placeholder="Hledat místa..." 
					class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
				/>
			</div>
			<select class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
				<option value="">Všechny kategorie</option>
				<option value="toilet">🚻 Toalety</option>
				<option value="wifi">📶 WiFi</option>
				<option value="running">🏃 Běhání</option>
				<option value="park">🌳 Parky</option>
				<option value="shop">🏪 Obchody</option>
			</select>
		</div>
	</div>

	<!-- Places grid -->
	<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
		{#each mockPlaces as place}
			<div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6">
				<div class="flex items-start justify-between mb-3">
					<div class="flex items-center space-x-2">
						<span class="text-2xl">{categoryIcons[place.category]}</span>
						<span class="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
							{categoryNames[place.category]}
						</span>
					</div>
					<div class="flex items-center space-x-1">
						<span class="text-yellow-400">⭐</span>
						<span class="text-sm text-gray-600">{place.rating}</span>
					</div>
				</div>
				
				<h3 class="text-lg font-semibold text-gray-900 mb-2">{place.name}</h3>
				<p class="text-gray-600 text-sm mb-3">{place.description}</p>
				<p class="text-gray-500 text-sm mb-4">📍 {place.address}</p>
				
				<div class="flex items-center justify-between">
					<span class="text-sm {place.isOpen ? 'text-green-600' : 'text-red-600'}">
						{place.isOpen ? '🟢 Otevřeno' : '🔴 Zavřeno'}
					</span>
					<button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
						Zobrazit detail →
					</button>
				</div>
			</div>
		{/each}
	</div>

	<!-- Empty state -->
	{#if mockPlaces.length === 0}
		<div class="text-center py-12">
			<div class="text-6xl mb-4">🗺️</div>
			<h3 class="text-lg font-medium text-gray-900 mb-2">Žádná místa nenalezena</h3>
			<p class="text-gray-500 mb-6">Zkuste změnit filtry nebo přidat nové místo.</p>
			<a 
				href="/add" 
				class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
			>
				Přidat první místo
			</a>
		</div>
	{/if}
</div>
