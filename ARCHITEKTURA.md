# 🗺️ Mapa Brna - Architektura a plán realizace

## 📝 Popis projektu
**Účel**: Zaznamenávání užitečných míst v Brně - Veřej<PERSON><PERSON> toalety, wifi s<PERSON>t<PERSON>, místa ka<PERSON> b<PERSON>hat, parky, ob<PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>, filtr podle otevírací doby atd.

## 🏗️ Architektura projektu

### 📋 Technický stack
- **Frontend & Backend**: SvelteKit (full-stack framework)
- **Styling**: Tailwind CSS
- **Mapa**: MapBox GL JS
- **Databáze**: Supabase (PostgreSQL + Auth + Storage)
- **Hosting**: Vercel
- **TypeScript**: Pro type safety

### 🗂️ Struktura databáze (Supabase)

#### Tabulky:
1. **places** - hlavní tabulka míst
   - id, name, description, category, latitude, longitude
   - opening_hours, contact_info, website, rating
   - created_at, updated_at, user_id

2. **categories** - kategorie míst
   - id, name, icon, color, description

3. **opening_hours** - <PERSON><PERSON><PERSON> ote<PERSON> doba
   - place_id, day_of_week, open_time, close_time, is_closed

4. **reviews** - hodnocení míst
   - id, place_id, user_id, rating, comment, created_at

5. **users** - uživatelé (Supabase Auth)

### 🎯 Kategorie míst:
- 🚻 Veřejné toalety
- 📶 WiFi zdarma
- 🏃 Místa na běhání
- 🌳 Parky
- 🏪 Večerky/obchody
- ☕ Kavárny
- 🍺 Restaurace/bary

### 📱 Funkcionality:
1. **Mapa s filtry** - podle kategorií, otevírací doby
2. **Přidávání míst** - formulář s validací
3. **Detail místa** - info, hodnocení, fotky
4. **Vyhledávání** - podle názvu, kategorie, lokace
5. **Uživatelské účty** - přihlášení, správa míst
6. **Responzivní design** - mobil first

## 📋 Plán realizace

### Fáze 1: Základní setup (1-2 dny) ✅ DOKONČENO
- [x] Inicializace SvelteKit projektu
- [x] Konfigurace Tailwind CSS
- [x] Setup Supabase projektu a databáze
- [x] Základní routing a layout
- [x] MapBox integrace s demo tokenem
- [x] Environment variables konfigurace

### Fáze 2: Databáze a API (2-3 dny)
- [ ] Vytvoření databázových tabulek v Supabase
- [ ] Nastavení RLS (Row Level Security)
- [ ] API endpointy pro CRUD operace
- [ ] Supabase client konfigurace

### Fáze 3: Mapová komponenta (2-3 dny)
- [ ] Integrace MapBox GL JS
- [ ] Zobrazení míst na mapě
- [ ] Interaktivní markery s popup
- [ ] Geolokace uživatele

### Fáze 4: UI komponenty (3-4 dny)
- [ ] Komponenty pro seznam míst
- [ ] Formulář pro přidání místa
- [ ] Detail místa
- [ ] Filtry a vyhledávání
- [ ] Responzivní navigace

### Fáze 5: Autentifikace (1-2 dny)
- [ ] Supabase Auth integrace
- [ ] Přihlášení/registrace
- [ ] Ochrana routes

### Fáze 6: Pokročilé funkce (2-3 dny)
- [ ] Hodnocení a komentáře
- [ ] Upload obrázků
- [ ] Pokročilé filtry (otevírací doba)
- [ ] Oblíbená místa

### Fáze 7: Optimalizace a deployment (1-2 dny)
- [ ] Performance optimalizace
- [ ] SEO meta tagy
- [ ] Vercel deployment
- [ ] Testování

## 📁 Vytvořené soubory

### Aplikace
- `src/routes/+layout.svelte` - Hlavní layout s navigací
- `src/routes/+page.svelte` - Homepage s MapBox mapou
- `src/routes/places/+page.svelte` - Seznam všech míst
- `src/routes/add/+page.svelte` - Formulář pro přidání místa
- `src/lib/supabase.ts` - Supabase client a databázové funkce

### Konfigurace
- `.env.example` - Příklad environment variables
- `.env` - Environment variables (s demo tokeny)
- `database/schema.sql` - SQL schéma pro Supabase
- `database/sample_data.sql` - Testovací data

### Závislosti
- `@supabase/supabase-js` - Supabase client
- `mapbox-gl` - MapBox GL JS
- `@types/mapbox-gl` - TypeScript typy

## 🚀 Další kroky
1. ✅ **Fáze 1 dokončena** - Základní setup aplikace
2. **Fáze 2** - Vytvořit Supabase projekt a spustit SQL skripty
3. **Fáze 3** - Implementovat načítání dat ze Supabase
4. **Fáze 4** - Dokončit mapové funkcionality

## 🔧 Jak spustit aplikaci
1. `npm install` - nainstalovat závislosti
2. `npm run dev` - spustit development server
3. Otevřít http://localhost:5174

## 📝 TODO
- [ ] Vytvořit Supabase projekt a získat credentials
- [ ] Spustit database/schema.sql v Supabase
- [ ] Aktualizovat .env s reálnými Supabase credentials
- [ ] Získat vlastní MapBox access token

---
*Vytvořeno: 23.7.2025*
*Aktualizováno: 23.7.2025 - Fáze 1 dokončena*
